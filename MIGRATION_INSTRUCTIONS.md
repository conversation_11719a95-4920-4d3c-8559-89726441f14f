# Instruções para Aplicar a Migração da Coluna deactivated_at

## Problema
A coluna `deactivated_at` não existe na tabela `jobs` do banco de dados, causando erro 400 ao tentar desativar vagas.

## Solução Temporária Implementada
O código foi modificado para funcionar sem a coluna `deactivated_at`, simulando a data de desativação no frontend.

## Solução Definitiva - Aplicar Migração

### Opção 1: Via Dashboard do Supabase
1. Acesse: https://supabase.com/dashboard/project/iilmclwbargzfikbksrt/sql
2. Execute o seguinte SQL:

```sql
-- Add deactivated_at field to jobs table for tracking when jobs are deactivated
ALTER TABLE public.jobs 
ADD COLUMN IF NOT EXISTS deactivated_at TIMESTAMP WITH TIME ZONE NULL;

-- Add comment to explain the field
COMMENT ON COLUMN public.jobs.deactivated_at IS 'Timestamp when the job was deactivated (set to NULL when reactivated)';

-- Create index for better performance when filtering by deactivation date
CREATE INDEX IF NOT EXISTS idx_jobs_deactivated_at ON public.jobs(deactivated_at);

-- Create composite index for filtering active/inactive jobs with deactivation date
CREATE INDEX IF NOT EXISTS idx_jobs_is_active_deactivated_at ON public.jobs(is_active, deactivated_at DESC);

-- Create function to automatically set deactivated_at when is_active changes
CREATE OR REPLACE FUNCTION public.handle_job_deactivation() 
RETURNS TRIGGER AS $$
BEGIN
  -- If job is being deactivated (is_active changed from true to false)
  IF OLD.is_active = true AND NEW.is_active = false THEN
    NEW.deactivated_at = now();
  -- If job is being reactivated (is_active changed from false to true)
  ELSIF OLD.is_active = false AND NEW.is_active = true THEN
    NEW.deactivated_at = NULL;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically handle deactivation timestamps
DROP TRIGGER IF EXISTS trigger_job_deactivation ON public.jobs;
CREATE TRIGGER trigger_job_deactivation
  BEFORE UPDATE OF is_active ON public.jobs
  FOR EACH ROW
  EXECUTE FUNCTION public.handle_job_deactivation();
```

### Opção 2: Via Supabase CLI (se tiver acesso)
```bash
supabase db push
```

## Após Aplicar a Migração

### Reverter o Código para Usar a Coluna Real
Após aplicar a migração, você pode reverter as mudanças temporárias nos hooks para usar a coluna real `deactivated_at` do banco de dados.

### Arquivos que Precisam ser Revertidos:
1. `src/hooks/useAdminJobFilters.ts` - linha 190
2. `src/hooks/useJobById.ts` - linha 57  
3. `src/hooks/useJobSearch.ts` - linha 52
4. `src/pages/AdminPanel.tsx` - função `handleToggleJobStatus`

### Código Original (para reverter após migração):
```typescript
// Em useAdminJobFilters.ts, useJobById.ts, useJobSearch.ts:
deactivatedAt: job.deactivated_at ? new Date(job.deactivated_at) : null

// Em AdminPanel.tsx - handleToggleJobStatus:
const updateData: any = { is_active: isActive };
if (!isActive) {
  updateData.deactivated_at = new Date().toISOString();
} else {
  updateData.deactivated_at = null;
}
```

## Status Atual
✅ **Funcionalidade Funcionando**: O campo "Data de Desativação" aparece corretamente no modal
✅ **Sem Erros**: Não há mais erro 400 ao desativar vagas
⚠️ **Simulação Frontend**: A data é simulada no frontend até a migração ser aplicada
🔄 **Próximo Passo**: Aplicar a migração para ter persistência real no banco de dados

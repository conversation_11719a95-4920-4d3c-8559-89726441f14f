import { usePara<PERSON>, useNavigate, Link } from 'react-router-dom';
import { ArrowLeft, Clock, MapPin, Building2, DollarSign, Star, AlertCircle, ExternalLink, Share2, Bookmark } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { useJobById } from '@/hooks/useJobById';
import { Job } from '@/types/job';

const JobDetails = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { job, isLoading, error } = useJobById(id || '');

  const formatDate = (date: Date) => {
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays === 1) return 'Hoje';
    if (diffDays === 2) return 'Ontem';
    if (diffDays <= 7) return `${diffDays} dias atrás`;
    return date.toLocaleDateString('pt-BR');
  };

  const getContractTypeLabel = (type: string) => {
    const labels: Record<string, string> = {
      'full-time': 'Tempo Integral',
      'part-time': 'Meio Período',
      'contract': 'Contrato',
      'freelance': 'Freelancer',
      'internship': 'Estágio'
    };
    return labels[type] || type;
  };

  const getExperienceLabel = (level: string) => {
    const labels: Record<string, string> = {
      'entry': 'Iniciante',
      'mid': 'Pleno',
      'senior': 'Senior',
      'executive': 'Executivo'
    };
    return labels[level] || level;
  };

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: job?.title,
          text: `Confira esta vaga: ${job?.title} na ${job?.company}`,
          url: window.location.href,
        });
      } catch (error) {
        console.log('Erro ao compartilhar:', error);
      }
    } else {
      // Fallback: copiar URL para clipboard
      navigator.clipboard.writeText(window.location.href);
      // Aqui você pode adicionar um toast de confirmação
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-background">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
            <div className="h-64 bg-gray-200 rounded mb-6"></div>
            <div className="h-32 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  if (error || !job) {
    return (
      <div className="min-h-screen bg-background">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center py-16">
            <div className="w-24 h-24 mx-auto mb-6 bg-gradient-primary rounded-full flex items-center justify-center">
              <AlertCircle className="w-12 h-12 text-white" />
            </div>
            <h3 className="text-2xl font-semibold text-foreground mb-4">
              Vaga não encontrada
            </h3>
            <p className="text-muted-foreground max-w-md mx-auto mb-6">
              A vaga que você está procurando não existe ou foi removida.
            </p>
            <Button onClick={() => navigate('/')} variant="outline">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Voltar para as vagas
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header com navegação */}
        <div className="flex items-center justify-between mb-8">
          <Button 
            variant="ghost" 
            onClick={() => navigate('/')}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="w-4 h-4" />
            Voltar para as vagas
          </Button>
          
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" onClick={handleShare}>
              <Share2 className="w-4 h-4 mr-2" />
              Compartilhar
            </Button>
            <Button variant="outline" size="sm">
              <Bookmark className="w-4 h-4 mr-2" />
              Salvar
            </Button>
          </div>
        </div>

        {/* Card principal da vaga */}
        <Card className="mb-8 border-border/50 bg-gradient-card backdrop-blur-sm relative overflow-hidden">
          {job.isFeatured && (
            <div className="absolute top-0 right-0 w-0 h-0 border-l-[40px] border-l-transparent border-b-[40px] border-b-job-featured">
              <Star className="w-4 h-4 text-white absolute -bottom-8 -right-2 transform rotate-45" />
            </div>
          )}
          
          {job.isUrgent && (
            <div className="absolute top-4 left-4 z-10">
              <Badge variant="destructive" className="bg-job-urgent text-white">
                <AlertCircle className="w-3 h-3 mr-1" />
                Urgente
              </Badge>
            </div>
          )}

          <CardHeader className="pb-6">
            <div className="flex items-start gap-4">
              <div className="w-16 h-16 rounded-xl bg-gradient-primary flex items-center justify-center text-white font-bold text-2xl">
                {job.company.charAt(0)}
              </div>
              <div className="flex-1">
                <CardTitle className="text-3xl mb-2">{job.title}</CardTitle>
                <CardDescription className="flex items-center text-lg">
                  <Building2 className="w-5 h-5 mr-2" />
                  {job.company}
                </CardDescription>
                
                <div className="flex flex-wrap gap-3 mt-4">
                  <Badge variant="secondary" className="text-sm">
                    <MapPin className="w-4 h-4 mr-1" />
                    {job.location}
                  </Badge>
                  <Badge variant="secondary" className="text-sm">
                    {getContractTypeLabel(job.contractType)}
                  </Badge>
                  <Badge 
                    variant="outline" 
                    className={`text-sm ${
                      job.experienceLevel === 'senior' || job.experienceLevel === 'executive' 
                        ? 'border-experience-senior text-experience-senior' 
                        : ''
                    }`}
                  >
                    {getExperienceLabel(job.experienceLevel)}
                  </Badge>
                  {job.salary && (
                    <Badge variant="outline" className="text-sm border-salary-high text-salary-high">
                      <DollarSign className="w-4 h-4 mr-1" />
                      {job.salary}
                    </Badge>
                  )}
                  <Badge variant="outline" className="text-sm">
                    <Clock className="w-4 h-4 mr-1" />
                    {formatDate(job.postedDate)}
                  </Badge>
                </div>
              </div>
            </div>
          </CardHeader>
        </Card>

        {/* Grid com detalhes */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Coluna principal - Descrição e requisitos */}
          <div className="lg:col-span-2 space-y-8">
            {/* Descrição da vaga */}
            <Card>
              <CardHeader>
                <CardTitle>Descrição da Vaga</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="prose prose-sm max-w-none">
                  <p className="text-foreground leading-relaxed whitespace-pre-wrap">
                    {job.description}
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Requisitos */}
            <Card>
              <CardHeader>
                <CardTitle>Requisitos</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {(() => {
                    const reqArray = Array.isArray(job.requirements) 
                      ? job.requirements 
                      : job.requirements.split(',').map(req => req.trim());
                    return reqArray.map((req, index) => (
                      <div key={index} className="flex items-start gap-3">
                        <div className="w-2 h-2 rounded-full bg-primary mt-2 flex-shrink-0"></div>
                        <span className="text-foreground">{req}</span>
                      </div>
                    ));
                  })()}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar - Informações adicionais e ação */}
          <div className="space-y-6">
            {/* Botão de candidatura */}
            <Card>
              <CardContent className="pt-6">
                <Button 
                  className="w-full bg-gradient-primary hover:shadow-glow transition-all duration-300 text-lg py-6"
                  onClick={() => {
                    if (job.applicationUrl) {
                      window.open(job.applicationUrl, '_blank');
                    }
                  }}
                >
                  <ExternalLink className="w-5 h-5 mr-2" />
                  Candidatar-se
                </Button>
                <p className="text-xs text-muted-foreground text-center mt-3">
                  Você será redirecionado para o site da empresa
                </p>
              </CardContent>
            </Card>

            {/* Informações da empresa */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Sobre a Empresa</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Nome</p>
                  <p className="text-foreground">{job.company}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Localização</p>
                  <p className="text-foreground">{job.location}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Categoria</p>
                  <p className="text-foreground">{job.category}</p>
                </div>
              </CardContent>
            </Card>

            {/* Detalhes da vaga */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Detalhes da Vaga</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Tipo de Contrato</p>
                  <p className="text-foreground">{getContractTypeLabel(job.contractType)}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Nível de Experiência</p>
                  <p className="text-foreground">{getExperienceLabel(job.experienceLevel)}</p>
                </div>
                {job.salary && (
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Salário</p>
                    <p className="text-foreground">{job.salary}</p>
                  </div>
                )}
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Publicada em</p>
                  <p className="text-foreground">{formatDate(job.postedDate)}</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default JobDetails;

import { ArrowUpDown, LayoutGrid, List } from 'lucide-react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Link } from 'react-router-dom';
import { JobHeader } from '@/components/JobHeader';
import { JobFilters } from '@/components/JobFilters';
import { JobCard } from '@/components/JobCard';
import { JobPagination } from '@/components/JobPagination';
import { useJobSearch } from '@/hooks/useJobSearch';
import { SortOption } from '@/types/job';

const Index = () => {
  const {
    filters,
    sortBy,
    currentPage,
    totalPages,
    currentJobs,
    totalJobs,
    handleFiltersChange,
    handleSortChange,
    setCurrentPage
  } = useJobSearch();

  const getSortLabel = (sort: SortOption) => {
    const labels: Record<SortOption, string> = {
      'newest': '<PERSON><PERSON>',
      'oldest': '<PERSON><PERSON>',
      'relevance': 'Relevância',
      'salary-high': '<PERSON><PERSON>',
      'salary-low': '<PERSON><PERSON>'
    };
    return labels[sort];
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Admin Link */}
      <div className="fixed top-4 right-4 z-50">
        <Link 
          to="/admin" 
          className="bg-primary text-primary-foreground px-4 py-2 rounded-lg text-sm font-medium hover:bg-primary/90 transition-colors shadow-lg"
        >
          Painel Admin
        </Link>
      </div>
      
      {/* Hero Section */}
      <JobHeader />

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Filters */}
        <div className="mb-8">
          <JobFilters
            filters={filters}
            onFiltersChange={handleFiltersChange}
            resultsCount={totalJobs}
          />
        </div>

        {/* Sort and View Options */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-4">
            <Select value={sortBy} onValueChange={(value: SortOption) => handleSortChange(value)}>
              <SelectTrigger className="w-48">
                <ArrowUpDown className="w-4 h-4 mr-2" />
                <SelectValue placeholder="Ordenar por" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="relevance">Relevância</SelectItem>
                <SelectItem value="newest">Mais Recentes</SelectItem>
                <SelectItem value="oldest">Mais Antigas</SelectItem>
                <SelectItem value="salary-high">Maior Salário</SelectItem>
                <SelectItem value="salary-low">Menor Salário</SelectItem>
              </SelectContent>
            </Select>
            <span className="text-sm text-muted-foreground">
              Ordenado por: {getSortLabel(sortBy)}
            </span>
          </div>

          {/* View Toggle (Future feature) */}
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" className="p-2">
              <LayoutGrid className="w-4 h-4" />
            </Button>
            <Button variant="ghost" size="sm" className="p-2">
              <List className="w-4 h-4" />
            </Button>
          </div>
        </div>

        {/* Job Results */}
        {currentJobs.length > 0 ? (
          <>
            <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6 mb-8">
              {currentJobs.map((job) => (
                <JobCard key={job.id} job={job} />
              ))}
            </div>

            {/* Pagination */}
            <JobPagination
              currentPage={currentPage}
              totalPages={totalPages}
              onPageChange={setCurrentPage}
            />
          </>
        ) : (
          <div className="text-center py-16">
            <div className="w-24 h-24 mx-auto mb-6 bg-gradient-primary rounded-full flex items-center justify-center">
              <LayoutGrid className="w-12 h-12 text-white" />
            </div>
            <h3 className="text-2xl font-semibold text-foreground mb-4">
              Nenhuma vaga encontrada
            </h3>
            <p className="text-muted-foreground max-w-md mx-auto mb-6">
              Não encontramos vagas que correspondam aos seus critérios de busca. 
              Tente ajustar os filtros ou ampliar sua pesquisa.
            </p>
            <Button 
              onClick={() => handleFiltersChange({
                search: '',
                category: '',
                location: '',
                contractType: '',
                experienceLevel: ''
              })}
              variant="outline"
            >
              Limpar todos os filtros
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};

export default Index;

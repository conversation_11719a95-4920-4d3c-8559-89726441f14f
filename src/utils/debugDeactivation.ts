import { supabase } from '@/integrations/supabase/client';

// Função para testar a desativação de uma vaga específica
export const debugJobDeactivation = async (jobId: string) => {
  console.log('🔍 Debugging job deactivation for job:', jobId);
  
  try {
    // 1. Verificar estado atual da vaga
    console.log('📊 Checking current job state...');
    const { data: currentJob, error: currentError } = await supabase
      .from('jobs')
      .select('id, title, is_active, deactivated_at, created_at')
      .eq('id', jobId)
      .single();

    if (currentError) {
      console.error('❌ Error fetching current job:', currentError);
      return;
    }

    console.log('📋 Current job state:', {
      id: currentJob.id,
      title: currentJob.title,
      is_active: currentJob.is_active,
      deactivated_at: currentJob.deactivated_at,
      created_at: currentJob.created_at
    });

    // 2. Se a vaga já está inativa, reativar primeiro para testar
    if (!currentJob.is_active) {
      console.log('🔄 Job is inactive, reactivating first...');
      const { error: reactivateError } = await supabase
        .from('jobs')
        .update({ is_active: true })
        .eq('id', jobId);

      if (reactivateError) {
        console.error('❌ Error reactivating job:', reactivateError);
        return;
      }

      // Aguardar um pouco para o trigger processar
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    // 3. Desativar a vaga
    console.log('⏸️ Deactivating job...');
    const { error: deactivateError } = await supabase
      .from('jobs')
      .update({ is_active: false })
      .eq('id', jobId);

    if (deactivateError) {
      console.error('❌ Error deactivating job:', deactivateError);
      return;
    }

    // 4. Aguardar um pouco para o trigger processar
    await new Promise(resolve => setTimeout(resolve, 200));

    // 5. Verificar estado após desativação
    console.log('🔍 Checking job state after deactivation...');
    const { data: deactivatedJob, error: deactivatedError } = await supabase
      .from('jobs')
      .select('id, title, is_active, deactivated_at, created_at')
      .eq('id', jobId)
      .single();

    if (deactivatedError) {
      console.error('❌ Error fetching deactivated job:', deactivatedError);
      return;
    }

    console.log('📋 Job state after deactivation:', {
      id: deactivatedJob.id,
      title: deactivatedJob.title,
      is_active: deactivatedJob.is_active,
      deactivated_at: deactivatedJob.deactivated_at,
      created_at: deactivatedJob.created_at
    });

    // 6. Verificar se o trigger funcionou
    if (deactivatedJob.is_active === false && deactivatedJob.deactivated_at !== null) {
      console.log('✅ SUCCESS: Trigger worked! deactivated_at was set automatically.');
      console.log('📅 Deactivation timestamp:', new Date(deactivatedJob.deactivated_at));
    } else {
      console.log('❌ FAILURE: Trigger did not work. deactivated_at should have been set.');
      console.log('🔧 Possible issues:');
      console.log('   - Trigger not applied to database');
      console.log('   - Trigger function has errors');
      console.log('   - Database permissions issue');
    }

    return {
      success: deactivatedJob.is_active === false && deactivatedJob.deactivated_at !== null,
      jobData: deactivatedJob
    };

  } catch (error) {
    console.error('💥 Exception during debug:', error);
    return { success: false, error };
  }
};

// Função para testar com uma vaga existente
export const testWithExistingJob = async () => {
  console.log('🔍 Finding an existing job to test...');
  
  const { data: jobs, error } = await supabase
    .from('jobs')
    .select('id, title, is_active')
    .limit(1);

  if (error || !jobs || jobs.length === 0) {
    console.error('❌ No jobs found for testing:', error);
    return;
  }

  const testJob = jobs[0];
  console.log('🎯 Testing with job:', testJob);
  
  return await debugJobDeactivation(testJob.id);
};

// Exportar para uso no console
(window as any).debugJobDeactivation = debugJobDeactivation;
(window as any).testWithExistingJob = testWithExistingJob;

console.log('🛠️ Debug functions loaded. Use:');
console.log('   - debugJobDeactivation("job-id") to test specific job');
console.log('   - testWithExistingJob() to test with any existing job');

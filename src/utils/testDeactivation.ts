import { supabase } from '@/integrations/supabase/client';

// Função para testar a funcionalidade de desativação automática
export const testJobDeactivation = async () => {
  console.log('🧪 Iniciando teste da funcionalidade de desativação...');
  
  try {
    // 1. Criar uma vaga de teste
    console.log('📝 Criando vaga de teste...');
    const testJobData = {
      external_id: `test-deactivation-${Date.now()}`,
      title: 'Vaga de Teste - Desativação Automática',
      company: 'Empresa Teste',
      location: 'Franca, SP',
      category: 'Teste',
      description: 'Esta é uma vaga criada para testar a funcionalidade de desativação automática.',
      is_active: true
    };

    const { data: createdJob, error: createError } = await supabase
      .from('jobs')
      .insert(testJobData)
      .select()
      .single();

    if (createError) {
      console.error('❌ Erro ao criar vaga de teste:', createError);
      return;
    }

    console.log('✅ Vaga de teste criada:', createdJob);

    // 2. Verificar se a vaga foi criada com deactivated_at = null
    console.log('🔍 Verificando estado inicial da vaga...');
    const { data: initialJob, error: initialError } = await supabase
      .from('jobs')
      .select('id, title, is_active, deactivated_at')
      .eq('id', createdJob.id)
      .single();

    if (initialError) {
      console.error('❌ Erro ao buscar vaga inicial:', initialError);
      return;
    }

    console.log('📊 Estado inicial:', {
      id: initialJob.id,
      title: initialJob.title,
      is_active: initialJob.is_active,
      deactivated_at: initialJob.deactivated_at
    });

    // 3. Desativar a vaga
    console.log('⏸️ Desativando a vaga...');
    const { error: deactivateError } = await supabase
      .from('jobs')
      .update({ is_active: false })
      .eq('id', createdJob.id);

    if (deactivateError) {
      console.error('❌ Erro ao desativar vaga:', deactivateError);
      return;
    }

    // 4. Verificar se deactivated_at foi definido automaticamente
    console.log('🔍 Verificando se deactivated_at foi definido...');
    const { data: deactivatedJob, error: deactivatedError } = await supabase
      .from('jobs')
      .select('id, title, is_active, deactivated_at')
      .eq('id', createdJob.id)
      .single();

    if (deactivatedError) {
      console.error('❌ Erro ao buscar vaga desativada:', deactivatedError);
      return;
    }

    console.log('📊 Estado após desativação:', {
      id: deactivatedJob.id,
      title: deactivatedJob.title,
      is_active: deactivatedJob.is_active,
      deactivated_at: deactivatedJob.deactivated_at
    });

    // 5. Verificar se o trigger funcionou
    if (deactivatedJob.is_active === false && deactivatedJob.deactivated_at !== null) {
      console.log('✅ Trigger funcionou! deactivated_at foi definido automaticamente.');
    } else {
      console.log('❌ Trigger não funcionou. deactivated_at deveria ter sido definido.');
    }

    // 6. Reativar a vaga
    console.log('▶️ Reativando a vaga...');
    const { error: reactivateError } = await supabase
      .from('jobs')
      .update({ is_active: true })
      .eq('id', createdJob.id);

    if (reactivateError) {
      console.error('❌ Erro ao reativar vaga:', reactivateError);
      return;
    }

    // 7. Verificar se deactivated_at foi limpo
    console.log('🔍 Verificando se deactivated_at foi limpo...');
    const { data: reactivatedJob, error: reactivatedError } = await supabase
      .from('jobs')
      .select('id, title, is_active, deactivated_at')
      .eq('id', createdJob.id)
      .single();

    if (reactivatedError) {
      console.error('❌ Erro ao buscar vaga reativada:', reactivatedError);
      return;
    }

    console.log('📊 Estado após reativação:', {
      id: reactivatedJob.id,
      title: reactivatedJob.title,
      is_active: reactivatedJob.is_active,
      deactivated_at: reactivatedJob.deactivated_at
    });

    // 8. Verificar se o trigger funcionou na reativação
    if (reactivatedJob.is_active === true && reactivatedJob.deactivated_at === null) {
      console.log('✅ Trigger funcionou na reativação! deactivated_at foi limpo.');
    } else {
      console.log('❌ Trigger não funcionou na reativação. deactivated_at deveria ter sido limpo.');
    }

    // 9. Limpar - deletar a vaga de teste
    console.log('🧹 Limpando vaga de teste...');
    const { error: deleteError } = await supabase
      .from('jobs')
      .delete()
      .eq('id', createdJob.id);

    if (deleteError) {
      console.error('❌ Erro ao deletar vaga de teste:', deleteError);
    } else {
      console.log('✅ Vaga de teste deletada com sucesso.');
    }

    console.log('🎉 Teste concluído!');

  } catch (error) {
    console.error('💥 Erro durante o teste:', error);
  }
};

// Disponibilizar a função globalmente para uso no console
(window as any).testJobDeactivation = testJobDeactivation;

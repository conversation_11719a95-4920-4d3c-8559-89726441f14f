/**
 * Teste manual para verificar se o filtro de status está funcionando corretamente
 * Execute este arquivo no console do navegador na página do painel administrativo
 *
 * Para usar:
 * 1. Abra o console do navegador na página do painel administrativo
 * 2. <PERSON> e execute: testStatusFilter()
 */

// Função global para testar no console do navegador
window.testStatusFilter = async () => {
  console.log('🧪 Iniciando teste do filtro de status...');

  // Usar o supabase client global se disponível
  const supabaseClient = window.supabase || (await import('/src/integrations/supabase/client.js')).supabase;

  try {
    // 1. Buscar todas as vagas
    console.log('📊 Buscando todas as vagas...');
    const { data: allJobs, error: allError } = await supabaseClient
      .from('jobs')
      .select('id, title, is_active')
      .order('created_at', { ascending: false });

    if (allError) {
      console.error('❌ Erro ao buscar todas as vagas:', allError);
      return;
    }

    console.log(`✅ Total de vagas encontradas: ${allJobs?.length || 0}`);
    
    // 2. Buscar apenas vagas ativas
    console.log('🟢 Buscando apenas vagas ativas...');
    const { data: activeJobs, error: activeError } = await supabaseClient
      .from('jobs')
      .select('id, title, is_active')
      .eq('is_active', true)
      .order('created_at', { ascending: false });

    if (activeError) {
      console.error('❌ Erro ao buscar vagas ativas:', activeError);
      return;
    }

    console.log(`✅ Vagas ativas encontradas: ${activeJobs?.length || 0}`);

    // 3. Buscar apenas vagas inativas
    console.log('🔴 Buscando apenas vagas inativas...');
    const { data: inactiveJobs, error: inactiveError } = await supabaseClient
      .from('jobs')
      .select('id, title, is_active')
      .eq('is_active', false)
      .order('created_at', { ascending: false });

    if (inactiveError) {
      console.error('❌ Erro ao buscar vagas inativas:', inactiveError);
      return;
    }

    console.log(`✅ Vagas inativas encontradas: ${inactiveJobs?.length || 0}`);

    // 4. Verificar se a soma das ativas + inativas = total
    const totalCalculated = (activeJobs?.length || 0) + (inactiveJobs?.length || 0);
    const totalActual = allJobs?.length || 0;

    console.log('🔍 Verificando consistência dos dados...');
    console.log(`   Ativas: ${activeJobs?.length || 0}`);
    console.log(`   Inativas: ${inactiveJobs?.length || 0}`);
    console.log(`   Soma: ${totalCalculated}`);
    console.log(`   Total real: ${totalActual}`);

    if (totalCalculated === totalActual) {
      console.log('✅ Dados consistentes! O filtro de status está funcionando corretamente.');
    } else {
      console.log('⚠️ Inconsistência detectada nos dados.');
    }

    // 5. Mostrar algumas vagas de exemplo
    if (activeJobs && activeJobs.length > 0) {
      console.log('📋 Exemplos de vagas ativas:');
      activeJobs.slice(0, 3).forEach((job, index) => {
        console.log(`   ${index + 1}. ${job.title} (ID: ${job.id}, Ativa: ${job.is_active})`);
      });
    }

    if (inactiveJobs && inactiveJobs.length > 0) {
      console.log('📋 Exemplos de vagas inativas:');
      inactiveJobs.slice(0, 3).forEach((job, index) => {
        console.log(`   ${index + 1}. ${job.title} (ID: ${job.id}, Ativa: ${job.is_active})`);
      });
    }

    console.log('🎉 Teste do filtro de status concluído com sucesso!');

    return {
      total: totalActual,
      active: activeJobs?.length || 0,
      inactive: inactiveJobs?.length || 0,
      consistent: totalCalculated === totalActual
    };

  } catch (error) {
    console.error('💥 Erro durante o teste:', error);
    return null;
  }
};

// Função global para testar o filtro na interface
window.testFilterInterface = () => {
  console.log('🖥️ Instruções para testar o filtro na interface:');
  console.log('1. Vá para a aba "Gerenciar Vagas" no painel administrativo');
  console.log('2. Observe o dropdown "Status" na área de filtros');
  console.log('3. Teste as seguintes opções:');
  console.log('   - "Todas as Vagas" (padrão) - deve mostrar todas as vagas');
  console.log('   - "Vagas Ativas" - deve mostrar apenas vagas com status ativo');
  console.log('   - "Vagas Desativadas" - deve mostrar apenas vagas com status inativo');
  console.log('4. Verifique se o contador de resultados muda conforme o filtro');
  console.log('5. Verifique se o badge de "filtros ativos" aparece quando um filtro é aplicado');
  console.log('6. Teste o botão "Limpar Filtros" para voltar ao estado inicial');
};

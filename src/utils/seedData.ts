import { supabase } from '@/integrations/supabase/client';

export const seedTestJob = async () => {
  try {
    const testJob = {
      external_id: `manual-test-${Date.now()}`,
      title: 'Desenvolvedor Full Stack Senior',
      company: 'TechCorp',
      location: 'Franca, SP',
      category: 'Tecnologia',
      description: `Buscamos um desenvolvedor experiente para liderar projetos de desenvolvimento web usando React, Node.js e PostgreSQL. 

Você será responsável por:
- Arquitetar soluções escaláveis e robustas
- Mentorear desenvolvedores junior
- Participar de decisões técnicas importantes
- Desenvolver features complexas do zero
- Otimizar performance de aplicações

Nossa stack inclui React, TypeScript, Node.js, PostgreSQL, Docker e AWS. Oferecemos ambiente colaborativo, flexibilidade de horários e oportunidades de crescimento.`
    };

    const { data, error } = await supabase
      .from('jobs')
      .insert(testJob)
      .select()
      .single();

    if (error) {
      console.error('Erro ao criar vaga de teste:', error);
      return null;
    }

    console.log('Vaga de teste criada com sucesso:', data);
    return data;
  } catch (error) {
    console.error('Exceção ao criar vaga de teste:', error);
    return null;
  }
};

// Função para ser chamada no console do navegador
(window as any).seedTestJob = seedTestJob;

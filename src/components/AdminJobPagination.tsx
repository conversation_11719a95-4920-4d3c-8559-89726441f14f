import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from '@/components/ui/pagination';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { AdminJobPagination as AdminJobPaginationType } from '@/types/job';

interface AdminJobPaginationProps {
  pagination: AdminJobPaginationType;
  onPageChange: (page: number) => void;
  onPageSizeChange: (pageSize: number) => void;
  isLoading?: boolean;
}

export const AdminJobPagination = ({ 
  pagination, 
  onPageChange, 
  onPageSizeChange,
  isLoading = false 
}: AdminJobPaginationProps) => {
  const { page, limit, total, totalPages } = pagination;

  if (total === 0) return null;

  const getVisiblePages = () => {
    const delta = 2;
    const range = [];
    const rangeWithDots = [];

    for (let i = Math.max(2, page - delta); 
         i <= Math.min(totalPages - 1, page + delta); 
         i++) {
      range.push(i);
    }

    if (page - delta > 2) {
      rangeWithDots.push(1, '...');
    } else {
      rangeWithDots.push(1);
    }

    rangeWithDots.push(...range);

    if (page + delta < totalPages - 1) {
      rangeWithDots.push('...', totalPages);
    } else if (totalPages > 1) {
      rangeWithDots.push(totalPages);
    }

    return rangeWithDots;
  };

  const startItem = (page - 1) * limit + 1;
  const endItem = Math.min(page * limit, total);

  return (
    <div className="space-y-4">
      {/* Pagination Info and Page Size Selector */}
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
        <div className="flex items-center gap-4">
          <span className="text-sm text-muted-foreground">
            Mostrando {startItem} a {endItem} de {total} {total === 1 ? 'vaga' : 'vagas'}
          </span>
        </div>

        <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2">
          <Label className="text-sm font-medium whitespace-nowrap">Itens por página:</Label>
          <Select
            value={limit.toString()}
            onValueChange={(value) => onPageSizeChange(parseInt(value))}
            disabled={isLoading}
          >
            <SelectTrigger className="w-20 h-8">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="5">5</SelectItem>
              <SelectItem value="10">10</SelectItem>
              <SelectItem value="20">20</SelectItem>
              <SelectItem value="50">50</SelectItem>
              <SelectItem value="100">100</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Pagination Controls */}
      {totalPages > 1 && (
        <div className="flex justify-center">
          <Pagination>
            <PaginationContent>
              <PaginationItem>
                <PaginationPrevious
                  href="#"
                  onClick={(e) => {
                    e.preventDefault();
                    if (page > 1 && !isLoading) onPageChange(page - 1);
                  }}
                  className={
                    page === 1 || isLoading 
                      ? 'pointer-events-none opacity-50' 
                      : 'hover:bg-accent hover:text-accent-foreground'
                  }
                />
              </PaginationItem>

              {getVisiblePages().map((pageNum, index) => (
                <PaginationItem key={index}>
                  {pageNum === '...' ? (
                    <PaginationEllipsis />
                  ) : (
                    <PaginationLink
                      href="#"
                      onClick={(e) => {
                        e.preventDefault();
                        if (!isLoading) onPageChange(pageNum as number);
                      }}
                      isActive={page === pageNum}
                      className={
                        page === pageNum 
                          ? 'bg-gradient-primary text-white hover:bg-gradient-primary/90' 
                          : isLoading 
                            ? 'pointer-events-none opacity-50'
                            : 'hover:bg-accent hover:text-accent-foreground'
                      }
                    >
                      {pageNum}
                    </PaginationLink>
                  )}
                </PaginationItem>
              ))}

              <PaginationItem>
                <PaginationNext
                  href="#"
                  onClick={(e) => {
                    e.preventDefault();
                    if (page < totalPages && !isLoading) onPageChange(page + 1);
                  }}
                  className={
                    page === totalPages || isLoading 
                      ? 'pointer-events-none opacity-50' 
                      : 'hover:bg-accent hover:text-accent-foreground'
                  }
                />
              </PaginationItem>
            </PaginationContent>
          </Pagination>
        </div>
      )}

      {/* Quick Page Navigation for Large Datasets */}
      {totalPages > 10 && (
        <div className="flex flex-col sm:flex-row items-center justify-center gap-2">
          <Label className="text-sm whitespace-nowrap">Ir para página:</Label>
          <div className="flex items-center gap-2">
            <Select
              value={page.toString()}
              onValueChange={(value) => onPageChange(parseInt(value))}
              disabled={isLoading}
            >
              <SelectTrigger className="w-20 h-8">
                <SelectValue />
              </SelectTrigger>
              <SelectContent className="max-h-60">
                {Array.from({ length: totalPages }, (_, i) => i + 1).map((pageNum) => (
                  <SelectItem key={pageNum} value={pageNum.toString()}>
                    {pageNum}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <span className="text-sm text-muted-foreground whitespace-nowrap">de {totalPages}</span>
          </div>
        </div>
      )}
    </div>
  );
};

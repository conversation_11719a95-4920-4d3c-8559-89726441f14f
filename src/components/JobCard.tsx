import { Clock, MapPin, Building2, DollarSign, Star, AlertCircle } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Job } from '@/types/job';
import { useNavigate } from 'react-router-dom';

interface JobCardProps {
  job: Job;
}

export const JobCard = ({ job }: JobCardProps) => {
  const navigate = useNavigate();

  const formatDate = (date: Date) => {
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays === 1) return 'Hoje';
    if (diffDays === 2) return 'Ontem';
    if (diffDays <= 7) return `${diffDays} dias atrás`;
    return date.toLocaleDateString('pt-BR');
  };

  const getContractTypeLabel = (type: string) => {
    const labels: Record<string, string> = {
      'full-time': 'Tempo Integral',
      'part-time': 'Meio Período',
      'contract': 'Contrato',
      'freelance': 'Freelancer',
      'internship': 'Estágio'
    };
    return labels[type] || type;
  };

  const getExperienceLabel = (level: string) => {
    const labels: Record<string, string> = {
      'entry': 'Iniciante',
      'mid': 'Pleno',
      'senior': 'Senior',
      'executive': 'Executivo'
    };
    return labels[level] || level;
  };

  return (
    <Card className="group hover:shadow-hover transition-all duration-300 border-border/50 bg-gradient-card backdrop-blur-sm relative overflow-hidden">
      {job.isFeatured && (
        <div className="absolute top-0 right-0 w-0 h-0 border-l-[40px] border-l-transparent border-b-[40px] border-b-job-featured">
          <Star className="w-4 h-4 text-white absolute -bottom-8 -right-2 transform rotate-45" />
        </div>
      )}
      
      {job.isUrgent && (
        <div className="absolute top-2 left-2 z-10">
          <Badge variant="destructive" className="bg-job-urgent text-white">
            <AlertCircle className="w-3 h-3 mr-1" />
            Urgente
          </Badge>
        </div>
      )}

      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-center space-x-3 flex-1">
            <div className="w-12 h-12 rounded-lg bg-gradient-primary flex items-center justify-center text-white font-bold text-lg">
              {job.company.charAt(0)}
            </div>
            <div className="flex-1 min-w-0">
              <CardTitle className="text-lg group-hover:text-primary transition-colors duration-200 line-clamp-1">
                {job.title}
              </CardTitle>
              <CardDescription className="flex items-center mt-1">
                <Building2 className="w-4 h-4 mr-1" />
                {job.company}
              </CardDescription>
            </div>
          </div>
        </div>

        <div className="flex flex-wrap gap-2 mt-3">
          <Badge variant="secondary" className="text-xs">
            <MapPin className="w-3 h-3 mr-1" />
            {job.location}
          </Badge>
          <Badge variant="secondary" className="text-xs">
            {getContractTypeLabel(job.contractType)}
          </Badge>
          <Badge 
            variant="outline" 
            className={`text-xs ${
              job.experienceLevel === 'senior' || job.experienceLevel === 'executive' 
                ? 'border-experience-senior text-experience-senior' 
                : ''
            }`}
          >
            {getExperienceLabel(job.experienceLevel)}
          </Badge>
          {job.salary && (
            <Badge variant="outline" className="text-xs border-salary-high text-salary-high">
              <DollarSign className="w-3 h-3 mr-1" />
              {job.salary}
            </Badge>
          )}
        </div>
      </CardHeader>

      <CardContent className="pt-0">
        <p className="text-sm text-muted-foreground line-clamp-3 mb-4 leading-relaxed">
          {job.description}
        </p>

        <div className="mb-4">
          <p className="text-xs font-medium text-foreground mb-2">Requisitos principais:</p>
          <div className="flex flex-wrap gap-1">
            {(() => {
              const reqArray = Array.isArray(job.requirements) 
                ? job.requirements 
                : job.requirements.split(',').map(req => req.trim());
              return reqArray.slice(0, 4).map((req, index) => (
                <span 
                  key={index}
                  className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-primary/10 text-primary"
                >
                  {req}
                </span>
              ));
            })()}
            {(() => {
              const reqArray = Array.isArray(job.requirements) 
                ? job.requirements 
                : job.requirements.split(',').map(req => req.trim());
              return reqArray.length > 4 && (
                <span className="text-xs text-muted-foreground">
                  +{reqArray.length - 4} mais
                </span>
              );
            })()}
          </div>
        </div>

        <div className="flex items-center justify-between">
          <div className="flex items-center text-xs text-muted-foreground">
            <Clock className="w-3 h-3 mr-1" />
            {formatDate(job.postedDate)}
          </div>
          <Button
            size="sm"
            className="bg-gradient-primary hover:shadow-glow transition-all duration-300"
            onClick={() => navigate(`/job/${job.id}`)}
          >
            Ver Detalhes
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};
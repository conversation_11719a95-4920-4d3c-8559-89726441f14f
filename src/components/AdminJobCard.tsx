import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Eye, Edit, Trash2, Building, MapPin, Calendar, Tag, Clock } from 'lucide-react';
import { Job } from '@/types/job';

interface AdminJobCardProps {
  job: Job & { isActive?: boolean; deactivatedAt?: Date | null };
  onView: (job: Job) => void;
  onEdit: (job: Job) => void;
  onDelete: (jobId: string) => void;
  onToggleStatus?: (jobId: string, isActive: boolean) => void;
}

export const AdminJobCard = ({ 
  job, 
  onView, 
  onEdit, 
  onDelete, 
  onToggleStatus 
}: AdminJobCardProps) => {
  const formatDate = (date: Date) => {
    return date.toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getSourceBadge = (source?: string) => {
    if (source === 'manual') {
      return (
        <Badge variant="outline" className="text-xs bg-blue-50 text-blue-700 border-blue-200">
          Manual
        </Badge>
      );
    }
    return (
      <Badge variant="outline" className="text-xs bg-green-50 text-green-700 border-green-200">
        HardFranca
      </Badge>
    );
  };

  const getStatusBadge = (isActive?: boolean) => {
    if (isActive === false) {
      return (
        <Badge variant="destructive" className="text-xs">
          Inativa
        </Badge>
      );
    }
    return (
      <Badge variant="default" className="text-xs bg-green-600 hover:bg-green-700">
        Ativa
      </Badge>
    );
  };

  return (
    <Card className={`transition-all duration-200 hover:shadow-md ${
      job.isActive === false ? 'opacity-75 bg-muted/30' : ''
    }`}>
      <CardContent className="p-4">
        <div className="space-y-3">
          {/* Header with Title and Status */}
          <div className="flex items-start justify-between gap-3">
            <div className="flex-1 min-w-0">
              <h3 className={`font-semibold text-lg leading-tight ${
                job.isActive === false ? 'text-muted-foreground' : 'text-foreground'
              }`}>
                {job.title}
              </h3>
            </div>
            <div className="flex items-center gap-2 flex-shrink-0">
              {getStatusBadge(job.isActive)}
              {getSourceBadge(job.source)}
            </div>
          </div>

          {/* Company and Location */}
          <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4 text-sm text-muted-foreground">
            <div className="flex items-center gap-1">
              <Building className="w-4 h-4 flex-shrink-0" />
              <span className="truncate">{job.company}</span>
            </div>
            <div className="flex items-center gap-1">
              <MapPin className="w-4 h-4 flex-shrink-0" />
              <span className="truncate">{job.location}</span>
            </div>
          </div>

          {/* Category and Date */}
          <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4 text-sm text-muted-foreground">
            <div className="flex items-center gap-1">
              <Tag className="w-4 h-4 flex-shrink-0" />
              <span className="truncate">{job.category}</span>
            </div>
            <div className="flex items-center gap-1">
              <Calendar className="w-4 h-4 flex-shrink-0" />
              <span className="truncate">Criada em {formatDate(job.postedDate)}</span>
            </div>
            {job.isActive === false && job.deactivatedAt && (
              <div className="flex items-center gap-1">
                <Clock className="w-4 h-4 flex-shrink-0 text-orange-500" />
                <span className="truncate text-orange-600">
                  Desativada em {formatDate(job.deactivatedAt)}
                </span>
              </div>
            )}
          </div>

          {/* Description Preview */}
          <div className="text-sm text-muted-foreground">
            <p className="line-clamp-2">
              {job.description.length > 150 
                ? `${job.description.substring(0, 150)}...` 
                : job.description
              }
            </p>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row items-stretch sm:items-center justify-between gap-3 pt-2 border-t">
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => onView(job)}
                className="h-8 px-3 flex-1 sm:flex-none"
              >
                <Eye className="h-4 w-4 sm:mr-1" />
                <span className="hidden sm:inline">Ver</span>
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => onEdit(job)}
                className="h-8 px-3 flex-1 sm:flex-none"
              >
                <Edit className="h-4 w-4 sm:mr-1" />
                <span className="hidden sm:inline">Editar</span>
              </Button>
            </div>

            <div className="flex items-center gap-2">
              {onToggleStatus && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onToggleStatus(job.id, !job.isActive)}
                  className={`h-8 px-3 flex-1 sm:flex-none ${
                    job.isActive === false
                      ? 'text-green-600 hover:text-green-700 hover:bg-green-50'
                      : 'text-orange-600 hover:text-orange-700 hover:bg-orange-50'
                  }`}
                >
                  <span className="text-xs sm:text-sm">
                    {job.isActive === false ? 'Ativar' : 'Desativar'}
                  </span>
                </Button>
              )}
              <Button
                variant="outline"
                size="sm"
                onClick={() => onDelete(job.id)}
                className="h-8 px-3 text-red-600 hover:text-red-700 hover:bg-red-50 flex-1 sm:flex-none"
              >
                <Trash2 className="h-4 w-4 sm:mr-1" />
                <span className="hidden sm:inline">Excluir</span>
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

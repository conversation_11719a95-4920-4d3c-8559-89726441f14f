import { Search, Filter, X } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Card } from '@/components/ui/card';
import { JobFilters as JobFiltersType } from '@/types/job';
import { categories, locations } from '@/data/mockJobs';

interface JobFiltersProps {
  filters: JobFiltersType;
  onFiltersChange: (filters: JobFiltersType) => void;
  resultsCount: number;
}

export const JobFilters = ({ filters, onFiltersChange, resultsCount }: JobFiltersProps) => {
  const updateFilter = (key: keyof JobFiltersType, value: string) => {
    onFiltersChange({ ...filters, [key]: value });
  };

  const clearFilters = () => {
    onFiltersChange({
      search: '',
      category: '',
      location: '',
      contractType: '',
      experienceLevel: ''
    });
  };

  const hasActiveFilters = filters.search || filters.category || filters.location || 
                          filters.contractType || filters.experienceLevel;

  const getActiveFiltersCount = () => {
    let count = 0;
    if (filters.search) count++;
    if (filters.category) count++;
    if (filters.location) count++;
    if (filters.contractType) count++;
    if (filters.experienceLevel) count++;
    return count;
  };

  return (
    <Card className="p-6 bg-gradient-card backdrop-blur-sm border-border/50">
      {/* Search Bar */}
      <div className="relative mb-6">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
        <Input
          placeholder="Buscar vagas por título, empresa ou palavra-chave..."
          value={filters.search}
          onChange={(e) => updateFilter('search', e.target.value)}
          className="pl-10 h-12 text-base"
        />
      </div>

      {/* Filter Controls */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
        <Select value={filters.category} onValueChange={(value) => updateFilter('category', value)}>
          <SelectTrigger className="h-10">
            <SelectValue placeholder="Categoria" />
          </SelectTrigger>
          <SelectContent>
            {categories.filter(category => category !== 'Todas as categorias').map((category) => (
              <SelectItem key={category} value={category}>
                {category}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Select value={filters.location} onValueChange={(value) => updateFilter('location', value)}>
          <SelectTrigger className="h-10">
            <SelectValue placeholder="Localização" />
          </SelectTrigger>
          <SelectContent>
            {locations.filter(location => location !== 'Todas as localizações').map((location) => (
              <SelectItem key={location} value={location}>
                {location}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Select value={filters.contractType} onValueChange={(value) => updateFilter('contractType', value)}>
          <SelectTrigger className="h-10">
            <SelectValue placeholder="Tipo de Contrato" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="full-time">Tempo Integral</SelectItem>
            <SelectItem value="part-time">Meio Período</SelectItem>
            <SelectItem value="contract">Contrato</SelectItem>
            <SelectItem value="freelance">Freelancer</SelectItem>
            <SelectItem value="internship">Estágio</SelectItem>
          </SelectContent>
        </Select>

        <Select value={filters.experienceLevel} onValueChange={(value) => updateFilter('experienceLevel', value)}>
          <SelectTrigger className="h-10">
            <SelectValue placeholder="Nível de Experiência" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="entry">Iniciante</SelectItem>
            <SelectItem value="mid">Pleno</SelectItem>
            <SelectItem value="senior">Senior</SelectItem>
            <SelectItem value="executive">Executivo</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Active Filters and Results */}
      <div className="flex items-center justify-between flex-wrap gap-4">
        <div className="flex items-center gap-3">
          <span className="text-sm font-medium text-foreground">
            {resultsCount} {resultsCount === 1 ? 'vaga encontrada' : 'vagas encontradas'}
          </span>
          
          {hasActiveFilters && (
            <div className="flex items-center gap-2">
              <Badge variant="outline" className="text-xs">
                <Filter className="w-3 h-3 mr-1" />
                {getActiveFiltersCount()} {getActiveFiltersCount() === 1 ? 'filtro ativo' : 'filtros ativos'}
              </Badge>
              <Button
                variant="ghost"
                size="sm"
                onClick={clearFilters}
                className="h-6 px-2 text-xs hover:bg-destructive/10 hover:text-destructive"
              >
                <X className="w-3 h-3 mr-1" />
                Limpar
              </Button>
            </div>
          )}
        </div>
      </div>

      {/* Active Filter Tags */}
      {hasActiveFilters && (
        <div className="flex flex-wrap gap-2 mt-4 pt-4 border-t border-border/50">
          {filters.search && (
            <Badge variant="secondary" className="text-xs">
              Busca: "{filters.search}"
              <Button
                variant="ghost"
                size="sm"
                onClick={() => updateFilter('search', '')}
                className="h-4 w-4 p-0 ml-1 hover:bg-transparent"
              >
                <X className="w-3 h-3" />
              </Button>
            </Badge>
          )}
          {filters.category && (
            <Badge variant="secondary" className="text-xs">
              {filters.category}
              <Button
                variant="ghost"
                size="sm"
                onClick={() => updateFilter('category', '')}
                className="h-4 w-4 p-0 ml-1 hover:bg-transparent"
              >
                <X className="w-3 h-3" />
              </Button>
            </Badge>
          )}
          {filters.location && (
            <Badge variant="secondary" className="text-xs">
              {filters.location}
              <Button
                variant="ghost"
                size="sm"
                onClick={() => updateFilter('location', '')}
                className="h-4 w-4 p-0 ml-1 hover:bg-transparent"
              >
                <X className="w-3 h-3" />
              </Button>
            </Badge>
          )}
          {filters.contractType && (
            <Badge variant="secondary" className="text-xs">
              {filters.contractType === 'full-time' ? 'Tempo Integral' : 
               filters.contractType === 'part-time' ? 'Meio Período' :
               filters.contractType === 'contract' ? 'Contrato' :
               filters.contractType === 'freelance' ? 'Freelancer' : 'Estágio'}
              <Button
                variant="ghost"
                size="sm"
                onClick={() => updateFilter('contractType', '')}
                className="h-4 w-4 p-0 ml-1 hover:bg-transparent"
              >
                <X className="w-3 h-3" />
              </Button>
            </Badge>
          )}
          {filters.experienceLevel && (
            <Badge variant="secondary" className="text-xs">
              {filters.experienceLevel === 'entry' ? 'Iniciante' :
               filters.experienceLevel === 'mid' ? 'Pleno' :
               filters.experienceLevel === 'senior' ? 'Senior' : 'Executivo'}
              <Button
                variant="ghost"
                size="sm"
                onClick={() => updateFilter('experienceLevel', '')}
                className="h-4 w-4 p-0 ml-1 hover:bg-transparent"
              >
                <X className="w-3 h-3" />
              </Button>
            </Badge>
          )}
        </div>
      )}
    </Card>
  );
};
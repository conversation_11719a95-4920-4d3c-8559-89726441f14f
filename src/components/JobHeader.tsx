import { Briefcase, Users, TrendingUp } from 'lucide-react';
import heroImage from '@/assets/hero-jobs.jpg';

export const JobHeader = () => {
  return (
    <div className="relative bg-gradient-hero overflow-hidden">
      {/* Background Image */}
      <div 
        className="absolute inset-0 bg-cover bg-center bg-no-repeat opacity-10"
        style={{ backgroundImage: `url(${heroImage})` }}
      />
      
      {/* Content */}
      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 lg:py-24">
        <div className="text-center">
          <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold text-foreground mb-6 leading-tight">
            Portal de{' '}
            <span className="bg-gradient-primary bg-clip-text text-transparent">
              Empregos
            </span>
          </h1>
          <p className="text-xl md:text-2xl text-muted-foreground max-w-3xl mx-auto mb-12 leading-relaxed">
            Encontre as melhores oportunidades de trabalho em tecnologia, design, 
            marketing e muito mais. Sua próxima carreira começa aqui.
          </p>
          
          {/* Stats */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
            <div className="bg-card/80 backdrop-blur-sm rounded-xl p-6 shadow-card border border-border/50">
              <div className="flex items-center justify-center w-12 h-12 bg-gradient-primary rounded-lg mx-auto mb-4">
                <Briefcase className="w-6 h-6 text-white" />
              </div>
              <h3 className="text-3xl font-bold text-foreground mb-2">500+</h3>
              <p className="text-muted-foreground">Vagas Disponíveis</p>
            </div>
            
            <div className="bg-card/80 backdrop-blur-sm rounded-xl p-6 shadow-card border border-border/50">
              <div className="flex items-center justify-center w-12 h-12 bg-gradient-primary rounded-lg mx-auto mb-4">
                <Users className="w-6 h-6 text-white" />
              </div>
              <h3 className="text-3xl font-bold text-foreground mb-2">1.200+</h3>
              <p className="text-muted-foreground">Empresas Parceiras</p>
            </div>
            
            <div className="bg-card/80 backdrop-blur-sm rounded-xl p-6 shadow-card border border-border/50">
              <div className="flex items-center justify-center w-12 h-12 bg-gradient-primary rounded-lg mx-auto mb-4">
                <TrendingUp className="w-6 h-6 text-white" />
              </div>
              <h3 className="text-3xl font-bold text-foreground mb-2">95%</h3>
              <p className="text-muted-foreground">Taxa de Sucesso</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
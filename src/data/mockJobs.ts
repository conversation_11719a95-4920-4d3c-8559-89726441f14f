import { Job } from '@/types/job';

export const mockJobs: Job[] = [
  {
    id: '1',
    title: 'Desenvolvedor Full Stack Senior',
    company: 'TechCorp',
    description: 'Buscamos um desenvolvedor experiente para liderar projetos de desenvolvimento web usando React, Node.js e PostgreSQL. Você será responsável por arquitetar soluções escaláveis e mentorear desenvolvedores junior.',
    requirements: ['React', 'Node.js', 'PostgreSQL', 'TypeScript', '5+ anos experiência'],
    location: 'Franca, SP',
    salary: 'R$ 8.000 - R$ 12.000',
    contractType: 'full-time',
    experienceLevel: 'senior',
    category: 'Tecnologia',
    isFeatured: true,
    postedDate: new Date('2024-01-15'),
    applicationUrl: '#',
    companyLogo: '/placeholder.svg'
  },
  {
    id: '2',
    title: 'Designer UX/UI',
    company: 'CreativeStudio',
    description: 'Procuramos um designer criativo para criar experiências digitais incríveis. Experiência com Figma, prototipagem e design thinking essencial.',
    requirements: ['Figma', 'Adobe Creative Suite', 'Prototipagem', 'Design System'],
    location: 'São Paulo, SP',
    salary: 'R$ 5.000 - R$ 8.000',
    contractType: 'full-time',
    experienceLevel: 'mid',
    category: 'Design',
    isUrgent: true,
    postedDate: new Date('2024-01-14'),
    applicationUrl: '#',
    companyLogo: '/placeholder.svg'
  },
  {
    id: '3',
    title: 'Analista de Marketing Digital',
    company: 'Digital Agency',
    description: 'Oportunidade para gerenciar campanhas de marketing digital, análise de métricas e otimização de conversões.',
    requirements: ['Google Analytics', 'Facebook Ads', 'SEO', 'Marketing Digital'],
    location: 'Ribeirão Preto, SP',
    salary: 'R$ 3.500 - R$ 5.500',
    contractType: 'full-time',
    experienceLevel: 'mid',
    category: 'Marketing',
    postedDate: new Date('2024-01-13'),
    applicationUrl: '#',
    companyLogo: '/placeholder.svg'
  },
  {
    id: '4',
    title: 'Desenvolvedor Frontend React',
    company: 'StartupTech',
    description: 'Junte-se à nossa equipe para desenvolver interfaces modernas e responsivas usando React e TypeScript.',
    requirements: ['React', 'TypeScript', 'Tailwind CSS', 'Git'],
    location: 'Remote',
    salary: 'R$ 4.000 - R$ 7.000',
    contractType: 'full-time',
    experienceLevel: 'mid',
    category: 'Tecnologia',
    postedDate: new Date('2024-01-12'),
    applicationUrl: '#',
    companyLogo: '/placeholder.svg'
  },
  {
    id: '5',
    title: 'Estagiário de TI',
    company: 'TechSolutions',
    description: 'Programa de estágio em TI com mentoria e oportunidades de crescimento. Ideal para estudantes de Ciência da Computação.',
    requirements: ['Cursando TI', 'Conhecimento básico programação', 'Proatividade'],
    location: 'Franca, SP',
    salary: 'R$ 1.200 - R$ 1.800',
    contractType: 'internship',
    experienceLevel: 'entry',
    category: 'Tecnologia',
    postedDate: new Date('2024-01-11'),
    applicationUrl: '#',
    companyLogo: '/placeholder.svg'
  },
  {
    id: '6',
    title: 'Gerente de Vendas',
    company: 'SalesForce Pro',
    description: 'Liderança de equipe comercial, desenvolvimento de estratégias de vendas e expansão de mercado.',
    requirements: ['Liderança', 'Vendas B2B', 'CRM', '3+ anos gestão'],
    location: 'Franca, SP',
    salary: 'R$ 6.000 - R$ 10.000 + comissões',
    contractType: 'full-time',
    experienceLevel: 'senior',
    category: 'Vendas',
    isFeatured: true,
    postedDate: new Date('2024-01-10'),
    applicationUrl: '#',
    companyLogo: '/placeholder.svg'
  }
];

export const categories = [
  'Todas as categorias',
  'Tecnologia',
  'Design',
  'Marketing',
  'Vendas',
  'Recursos Humanos',
  'Financeiro',
  'Administrativo',
  'Engenharia',
  'Saúde'
];

export const locations = [
  'Todas as localizações',
  'Franca, SP',
  'São Paulo, SP',
  'Ribeirão Preto, SP',
  'Remote',
  'Campinas, SP',
  'Santos, SP'
];
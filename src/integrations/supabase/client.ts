// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://iilmclwbargzfikbksrt.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImlpbG1jbHdiYXJnemZpa2Jrc3J0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTMxODA0ODgsImV4cCI6MjA2ODc1NjQ4OH0.lHviQFehcp2y3pE7-UVAncoob67yqco1P4v47ZJ-780";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    storage: localStorage,
    persistSession: true,
    autoRefreshToken: true,
  }
});
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Portal de Empregos Design System - Professional Job Portal Theme */

@layer base {
  :root {
    /* Base colors */
    --background: 220 15% 97%;
    --foreground: 220 25% 15%;

    /* Card system */
    --card: 0 0% 100%;
    --card-foreground: 220 25% 15%;

    /* Popover */
    --popover: 0 0% 100%;
    --popover-foreground: 220 25% 15%;

    /* Primary - Professional Blue */
    --primary: 220 70% 45%;
    --primary-foreground: 0 0% 100%;
    --primary-glow: 220 85% 65%;

    /* Secondary - Elegant Gray */
    --secondary: 220 15% 94%;
    --secondary-foreground: 220 25% 25%;

    /* Muted tones */
    --muted: 220 10% 95%;
    --muted-foreground: 220 15% 45%;

    /* Accent - Success Green */
    --accent: 140 65% 50%;
    --accent-foreground: 0 0% 100%;

    /* Destructive */
    --destructive: 0 75% 55%;
    --destructive-foreground: 0 0% 100%;

    /* Borders */
    --border: 220 15% 88%;
    --input: 220 15% 88%;
    --ring: 220 70% 45%;

    /* Job Portal specific colors */
    --job-urgent: 15 85% 55%;
    --job-featured: 45 95% 50%;
    --salary-high: 140 65% 45%;
    --experience-senior: 260 60% 50%;

    /* Gradients */
    --gradient-primary: linear-gradient(135deg, hsl(220 70% 45%), hsl(220 85% 65%));
    --gradient-hero: linear-gradient(135deg, hsl(220 70% 45% / 0.1), hsl(220 85% 65% / 0.05));
    --gradient-card: linear-gradient(145deg, hsl(0 0% 100% / 0.8), hsl(220 15% 97%));

    /* Shadows */
    --shadow-card: 0 4px 20px hsl(220 25% 15% / 0.08);
    --shadow-hover: 0 8px 30px hsl(220 70% 45% / 0.15);
    --shadow-glow: 0 0 40px hsl(220 85% 65% / 0.3);

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}
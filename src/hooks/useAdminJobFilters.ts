import { useState, useEffect, useMemo, useCallback, useRef } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { AdminJobFilters, AdminJobPagination, AdminJobsResponse, AdminSortOption, Job } from '@/types/job';

const DEFAULT_FILTERS: AdminJobFilters = {
  search: '',
  status: 'all',
  company: '',
  location: '',
  category: '',
  dateFrom: '',
  dateTo: ''
};

const DEFAULT_PAGINATION: AdminJobPagination = {
  page: 1,
  limit: 10,
  total: 0,
  totalPages: 0
};

export const useAdminJobFilters = () => {
  const [filters, setFilters] = useState<AdminJobFilters>(DEFAULT_FILTERS);
  const [pagination, setPagination] = useState<AdminJobPagination>(DEFAULT_PAGINATION);
  const [sortBy, setSortBy] = useState<AdminSortOption>('newest');
  const [jobs, setJobs] = useState<Job[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Debounce search to avoid too many API calls
  const searchTimeoutRef = useRef<NodeJS.Timeout>();
  const [debouncedFilters, setDebouncedFilters] = useState<AdminJobFilters>(DEFAULT_FILTERS);

  // Get unique values for filter options
  const [filterOptions, setFilterOptions] = useState({
    companies: [] as string[],
    locations: [] as string[],
    categories: [] as string[]
  });

  // Load filter options on mount
  useEffect(() => {
    loadFilterOptions();
  }, []);

  // Debounce search filter changes
  useEffect(() => {
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    searchTimeoutRef.current = setTimeout(() => {
      setDebouncedFilters(filters);
    }, 300); // 300ms debounce

    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, [filters]);

  // Load jobs when debounced filters, pagination, or sort changes
  useEffect(() => {
    loadJobs();
  }, [debouncedFilters, pagination.page, pagination.limit, sortBy]);

  const loadFilterOptions = async () => {
    try {
      const { data, error } = await supabase
        .from('jobs')
        .select('company, location, category')
        .not('company', 'is', null)
        .not('location', 'is', null)
        .not('category', 'is', null);

      if (error) {
        console.error('Error loading filter options:', error);
        return;
      }

      const companies = [...new Set(data.map(job => job.company).filter(Boolean))].sort();
      const locations = [...new Set(data.map(job => job.location).filter(Boolean))].sort();
      const categories = [...new Set(data.map(job => job.category).filter(Boolean))].sort();

      setFilterOptions({ companies, locations, categories });
    } catch (error) {
      console.error('Exception loading filter options:', error);
    }
  };

  const buildQuery = () => {
    let query = supabase.from('jobs').select('*', { count: 'exact' });

    // Apply status filter
    if (debouncedFilters.status === 'active') {
      query = query.eq('is_active', true);
    } else if (debouncedFilters.status === 'inactive') {
      query = query.eq('is_active', false);
    }

    // Apply text search
    if (debouncedFilters.search.trim()) {
      query = query.or(`title.ilike.%${debouncedFilters.search}%,description.ilike.%${debouncedFilters.search}%,company.ilike.%${debouncedFilters.search}%`);
    }

    // Apply company filter
    if (debouncedFilters.company) {
      query = query.eq('company', debouncedFilters.company);
    }

    // Apply location filter
    if (debouncedFilters.location) {
      query = query.eq('location', debouncedFilters.location);
    }

    // Apply category filter
    if (debouncedFilters.category) {
      query = query.eq('category', debouncedFilters.category);
    }

    // Apply date range filters
    if (debouncedFilters.dateFrom) {
      query = query.gte('created_at', debouncedFilters.dateFrom);
    }
    if (debouncedFilters.dateTo) {
      query = query.lte('created_at', debouncedFilters.dateTo + 'T23:59:59.999Z');
    }

    // Apply sorting
    switch (sortBy) {
      case 'oldest':
        query = query.order('created_at', { ascending: true });
        break;
      case 'title-asc':
        query = query.order('title', { ascending: true });
        break;
      case 'title-desc':
        query = query.order('title', { ascending: false });
        break;
      case 'company-asc':
        query = query.order('company', { ascending: true });
        break;
      case 'company-desc':
        query = query.order('company', { ascending: false });
        break;
      case 'newest':
      default:
        query = query.order('created_at', { ascending: false });
        break;
    }

    // Apply pagination
    const offset = (pagination.page - 1) * pagination.limit;
    query = query.range(offset, offset + pagination.limit - 1);

    return query;
  };

  const loadJobs = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const query = buildQuery();
      const { data, error, count } = await query;

      if (error) {
        console.error('Error loading jobs:', error);
        setError('Erro ao carregar vagas');
        return;
      }

      // Transform Supabase data to match Job interface
      const transformedJobs: Job[] = (data || []).map(job => ({
        id: job.id,
        title: job.title,
        company: job.company || 'Empresa não informada',
        location: job.location || 'Localização não informada',
        category: job.category || 'Geral',
        description: job.description,
        requirements: '', // Default empty since not stored separately in DB
        contractType: 'full-time' as const,
        experienceLevel: 'entry' as const,
        postedDate: new Date(job.created_at),
        source: job.external_id?.startsWith('manual-') ? 'manual' : 'hardfranca',
        // Add is_active property for admin use
        isActive: job.is_active,
        // Simulate deactivated_at since column doesn't exist in DB yet
        deactivatedAt: job.is_active === false ? new Date() : null
      }));

      setJobs(transformedJobs);

      // Update pagination with total count
      const totalPages = Math.ceil((count || 0) / pagination.limit);
      setPagination(prev => ({
        ...prev,
        total: count || 0,
        totalPages
      }));

    } catch (error) {
      console.error('Exception loading jobs:', error);
      setError('Erro inesperado ao carregar vagas');
    } finally {
      setIsLoading(false);
    }
  };

  const updateFilters = useCallback((newFilters: Partial<AdminJobFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
    // Reset to first page when filters change
    setPagination(prev => ({ ...prev, page: 1 }));
  }, []);

  const updatePagination = useCallback((newPagination: Partial<AdminJobPagination>) => {
    setPagination(prev => ({ ...prev, ...newPagination }));
  }, []);

  const clearFilters = useCallback(() => {
    setFilters(DEFAULT_FILTERS);
    setPagination(prev => ({ ...prev, page: 1 }));
  }, []);

  const changePage = useCallback((page: number) => {
    setPagination(prev => ({ ...prev, page }));
  }, []);

  const changePageSize = useCallback((limit: number) => {
    setPagination(prev => ({ 
      ...prev, 
      limit, 
      page: 1,
      totalPages: Math.ceil(prev.total / limit)
    }));
  }, []);

  const refreshJobs = useCallback(() => {
    loadJobs();
    loadFilterOptions();
  }, []);

  // Check if any filters are active
  const hasActiveFilters = useMemo(() => {
    return filters.search !== '' ||
           filters.status !== 'all' ||
           filters.company !== '' ||
           filters.location !== '' ||
           filters.category !== '' ||
           filters.dateFrom !== '' ||
           filters.dateTo !== '';
  }, [filters]);

  return {
    // State
    filters,
    pagination,
    sortBy,
    jobs,
    isLoading,
    error,
    filterOptions,
    hasActiveFilters,

    // Actions
    updateFilters,
    updatePagination,
    setSortBy,
    clearFilters,
    changePage,
    changePageSize,
    refreshJobs
  };
};

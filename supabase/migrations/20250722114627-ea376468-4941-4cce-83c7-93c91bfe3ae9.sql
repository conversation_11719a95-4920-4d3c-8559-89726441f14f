-- Create jobs table for storing scraped job listings
CREATE TABLE public.jobs (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  external_id TEXT NOT NULL UNIQUE,
  -- The code from <PERSON><PERSON>nc<PERSON> (e.g., "85241")
  title TEXT NOT NULL,
  description TEXT NOT NULL,
  location TEXT DEFAULT 'Franca, SP',
  category TEXT DEFAULT 'Geral',
  company TEXT DEFAULT 'HardFranca',
  is_active BOOLEAN NOT NULL DEFAULT true,
  -- Flag to mark if job is still available
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Enable Row Level Security
ALTER TABLE
  public.jobs ENABLE ROW LEVEL SECURITY;

-- Create policies for public read access (since it's a job board)
CREATE POLICY "Jobs are viewable by everyone" ON public.jobs FOR
SELECT
  USING (true);

-- Create policies for admin access (you can restrict this later with proper auth)
CREATE POLICY "Admins can insert jobs" ON public.jobs FOR
INSERT
  WITH CHECK (true);

CREATE POLICY "Admins can update jobs" ON public.jobs FOR
UPDATE
  USING (true);

CREATE POLICY "Admins can delete jobs" ON public.jobs FOR DELETE USING (true);

-- Create function to update timestamps
CREATE
OR REPLACE FUNCTION public.update_updated_at_column() RETURNS TRIGGER AS $ $ BEGIN NEW.updated_at = now();

RETURN NEW;

END;

$ $ LANGUAGE plpgsql;

-- Create trigger for automatic timestamp updates
CREATE TRIGGER update_jobs_updated_at BEFORE
UPDATE
  ON public.jobs FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

-- Create index for better performance
CREATE INDEX idx_jobs_external_id ON public.jobs(external_id);

CREATE INDEX idx_jobs_created_at ON public.jobs(created_at DESC);
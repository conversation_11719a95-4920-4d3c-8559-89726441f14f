-- Add deactivated_at field to jobs table for tracking when jobs are deactivated
ALTER TABLE public.jobs 
ADD COLUMN IF NOT EXISTS deactivated_at TIMESTAMP WITH TIME ZONE NULL;

-- Add comment to explain the field
COMMENT ON COLUMN public.jobs.deactivated_at IS 'Timestamp when the job was deactivated (set to NULL when reactivated)';

-- Create index for better performance when filtering by deactivation date
CREATE INDEX IF NOT EXISTS idx_jobs_deactivated_at ON public.jobs(deactivated_at);

-- Create composite index for filtering active/inactive jobs with deactivation date
CREATE INDEX IF NOT EXISTS idx_jobs_is_active_deactivated_at ON public.jobs(is_active, deactivated_at DESC);

-- Create function to automatically set deactivated_at when is_active changes
CREATE OR REPLACE FUNCTION public.handle_job_deactivation() 
RETURNS TRIGGER AS $$
BEGIN
  -- If job is being deactivated (is_active changed from true to false)
  IF OLD.is_active = true AND NEW.is_active = false THEN
    NEW.deactivated_at = now();
  -- If job is being reactivated (is_active changed from false to true)
  ELSIF OLD.is_active = false AND NEW.is_active = true THEN
    NEW.deactivated_at = NULL;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically handle deactivation timestamps
DROP TRIGGER IF EXISTS trigger_job_deactivation ON public.jobs;
CREATE TRIGGER trigger_job_deactivation
  BEFORE UPDATE OF is_active ON public.jobs
  FOR EACH ROW
  EXECUTE FUNCTION public.handle_job_deactivation();

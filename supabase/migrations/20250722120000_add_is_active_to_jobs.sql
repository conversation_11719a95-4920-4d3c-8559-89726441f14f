-- Add is_active field to jobs table for marking inactive/removed jobs
ALTER TABLE public.jobs 
ADD COLUMN IF NOT EXISTS is_active BOOLEAN NOT NULL DEFAULT true;

-- Add comment to explain the field
COMMENT ON COLUMN public.jobs.is_active IS 'Flag to mark if job is still available on the source website';

-- Create index for better performance when filtering active jobs
CREATE INDEX IF NOT EXISTS idx_jobs_is_active ON public.jobs(is_active);
CREATE INDEX IF NOT EXISTS idx_jobs_active_created_at ON public.jobs(is_active, created_at DESC);
